# Copy this file to .env and fill in your values

# Environment Mode
# Valid values: local, staging, production
ENV_MODE=local

#DATABASE
SUPABASE_URL=
SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SSL=false

RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672

# LLM Providers:
ANTHROPIC_API_KEY=
OPENAI_API_KEY=
MODEL_TO_USE=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION_NAME=

GROQ_API_KEY=
OPENROUTER_API_KEY=

# DATA APIS
RAPID_API_KEY=

# WEB SEARCH
TAVILY_API_KEY=

# WEB SCRAPE
FIRECRAWL_API_KEY=
FIRECRAWL_URL=

# Sandbox container provider:
DAYTONA_API_KEY=
DAYTONA_SERVER_URL=
DAYTONA_TARGET=

LANGFUSE_PUBLIC_KEY="pk-REDACTED"
LANGFUSE_SECRET_KEY="sk-REDACTED"
LANGFUSE_HOST="https://cloud.langfuse.com"

<<<<<<< HEAD
# Stripe Configuration
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_SECRET_KEY_HERE
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_PUBLISHABLE_KEY_HERE
STRIPE_WEBHOOK_SECRET=whsec_YOUR_WEBHOOK_SECRET_HERE
STRIPE_PRODUCT_ID=prod_YOUR_PRODUCT_ID_HERE

SMITHERY_API_KEY=
=======
SMITHERY_API_KEY=

MCP_CREDENTIAL_ENCRYPTION_KEY=
>>>>>>> 7899ad2e622ce4edadb37951665f970e3582562a
